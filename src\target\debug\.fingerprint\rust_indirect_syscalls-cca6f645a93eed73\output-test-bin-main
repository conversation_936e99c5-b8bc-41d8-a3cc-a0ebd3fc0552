{"$message_type":"diagnostic","message":"only foreign, `unsafe extern \"C\"`, or `unsafe extern \"C-unwind\"` functions may have a C-variadic arg","code":null,"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":3688,"byte_end":3691,"line_start":84,"line_end":84,"column_start":113,"column_end":116,"is_primary":true,"text":[{"text":"    #[allow(non_snake_case)] pub unsafe fn CallR12(_func: *mut c_void, _arg_count: usize, _gadget: *mut c_void, ...) -> *mut c_void { null_mut() }","highlight_start":113,"highlight_end":116}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: only foreign, `unsafe extern \"C\"`, or `unsafe extern \"C-unwind\"` functions may have a C-variadic arg\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:84:113\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[allow(non_snake_case)] pub unsafe fn CallR12(_func: *mut c_void, _arg_count: usize, _gadget: *mut c_void, ...) -> *mut c_void { nul\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `c_void` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":5884,"byte_end":5890,"line_start":134,"line_end":134,"column_start":64,"column_end":70,"is_primary":true,"text":[{"text":"        pub fn log_address(&mut self, name: &str, addr: *const c_void) { self.info(&format!(\"{}: {:p}\", name, addr)); }","highlight_start":64,"highlight_end":70}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these items","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4494,"byte_end":4494,"line_start":109,"line_end":109,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use std::fs::{File, OpenOptions};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use crate::c_void;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\main.rs","byte_start":4494,"byte_end":4494,"line_start":109,"line_end":109,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use std::fs::{File, OpenOptions};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use std::ffi::c_void;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\main.rs","byte_start":4494,"byte_end":4494,"line_start":109,"line_end":109,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use std::fs::{File, OpenOptions};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use std::os::raw::c_void;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\main.rs","byte_start":4494,"byte_end":4494,"line_start":109,"line_end":109,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use std::fs::{File, OpenOptions};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use core::ffi::c_void;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `c_void` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:134:64\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        pub fn log_address(&mut self, name: &str, addr: *const c_void) { self.info(&format!(\"{}: {:p}\", name, addr)); }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these items\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse crate::c_void;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse std::ffi::c_void;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse std::os::raw::c_void;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse core::ffi::c_void;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"C-variadic functions are unstable","code":{"code":"E0658","explanation":"An unstable feature was used.\n\nErroneous code example:\n\n```compile_fail,E0658\nuse std::intrinsics; // error: use of unstable library feature `core_intrinsics`\n```\n\nIf you're using a stable or a beta version of rustc, you won't be able to use\nany unstable features. In order to do so, please switch to a nightly version of\nrustc (by using [rustup]).\n\nIf you're using a nightly version of rustc, just add the corresponding feature\nto be able to use it:\n\n```\n#![feature(core_intrinsics)]\n\nuse std::intrinsics; // ok!\n```\n\n[rustup]: https://rust-lang.github.io/rustup/concepts/channels.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":3605,"byte_end":3722,"line_start":84,"line_end":84,"column_start":30,"column_end":147,"is_primary":true,"text":[{"text":"    #[allow(non_snake_case)] pub unsafe fn CallR12(_func: *mut c_void, _arg_count: usize, _gadget: *mut c_void, ...) -> *mut c_void { null_mut() }","highlight_start":30,"highlight_end":147}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"see issue #44930 <https://github.com/rust-lang/rust/issues/44930> for more information","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"add `#![feature(c_variadic)]` to the crate attributes to enable","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"this compiler was built on 2025-06-25; consider upgrading it if it is out of date","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0658]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: C-variadic functions are unstable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:84:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m_case)] pub unsafe fn CallR12(_func: *mut c_void, _arg_count: usize, _gadget: *mut c_void, ...) -> *mut c_void { null_mut() }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: see issue #44930 <https://github.com/rust-lang/rust/issues/44930> for more information\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: add `#![feature(c_variadic)]` to the crate attributes to enable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this compiler was built on 2025-06-25; consider upgrading it if it is out of date\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `null`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":837,"byte_end":841,"line_start":19,"line_end":19,"column_start":16,"column_end":20,"is_primary":true,"text":[{"text":"use std::ptr::{null, null_mut};","highlight_start":16,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":837,"byte_end":843,"line_start":19,"line_end":19,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use std::ptr::{null, null_mut};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":836,"byte_end":837,"line_start":19,"line_end":19,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use std::ptr::{null, null_mut};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":851,"byte_end":852,"line_start":19,"line_end":19,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use std::ptr::{null, null_mut};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `null`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:19:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::ptr::{null, null_mut};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `windows_sys::Win32::System::SystemInformation::GetTickCount64`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":1373,"byte_end":1434,"line_start":32,"line_end":32,"column_start":5,"column_end":66,"is_primary":true,"text":[{"text":"use windows_sys::Win32::System::SystemInformation::GetTickCount64;","highlight_start":5,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":1369,"byte_end":1437,"line_start":32,"line_end":33,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use windows_sys::Win32::System::SystemInformation::GetTickCount64;","highlight_start":1,"highlight_end":67},{"text":"use windows_sys::Win32::System::SystemServices::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `windows_sys::Win32::System::SystemInformation::GetTickCount64`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:32:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse windows_sys::Win32::System::SystemInformation::GetTickCount64;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":34346,"byte_end":34347,"line_start":788,"line_end":788,"column_start":51,"column_end":52,"is_primary":true,"text":[{"text":"        let ascii_part = chunk.iter().map(|&b| if (b >= 32 && b <= 126) { b as char } else { '.' }).collect::<String>();","highlight_start":51,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":34366,"byte_end":34367,"line_start":788,"line_end":788,"column_start":71,"column_end":72,"is_primary":true,"text":[{"text":"        let ascii_part = chunk.iter().map(|&b| if (b >= 32 && b <= 126) { b as char } else { '.' }).collect::<String>();","highlight_start":71,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_parens)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":34346,"byte_end":34347,"line_start":788,"line_end":788,"column_start":51,"column_end":52,"is_primary":true,"text":[{"text":"        let ascii_part = chunk.iter().map(|&b| if (b >= 32 && b <= 126) { b as char } else { '.' }).collect::<String>();","highlight_start":51,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":34366,"byte_end":34367,"line_start":788,"line_end":788,"column_start":71,"column_end":72,"is_primary":true,"text":[{"text":"        let ascii_part = chunk.iter().map(|&b| if (b >= 32 && b <= 126) { b as char } else { '.' }).collect::<String>();","highlight_start":71,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:788:51\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m788\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ascii_part = chunk.iter().map(|&b| if (b >= 32 && b <= 126) { b as char } else { '.' }).collect::<String>();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_parens)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m788\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let ascii_part = chunk.iter().map(|&b| if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mb >= 32 && b <= 126\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m { b as char } else { '.' }).collect::<String>();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m788\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let ascii_part = chunk.iter().map(|&b| if b >= 32 && b <= 126 { b as char } else { '.' }).collect::<String>();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `as_ref` found for struct `Mutex` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":8644,"byte_end":8650,"line_start":206,"line_end":206,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"    unsafe { (*APP_CONTEXT.as_ptr()).as_ref().unwrap() }","highlight_start":38,"highlight_end":44}],"label":"method not found in `Mutex<AppContext>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `as_ref` found for struct `Mutex` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:206:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { (*APP_CONTEXT.as_ptr()).as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Mutex<AppContext>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":43966,"byte_end":43973,"line_start":947,"line_end":947,"column_start":16,"column_end":23,"is_primary":true,"text":[{"text":"    for (name, &module, func_name) in functions_to_test.iter() {","highlight_start":16,"highlight_end":23}],"label":"expected `*mut u8`, found `&_`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":43989,"byte_end":44013,"line_start":947,"line_end":947,"column_start":39,"column_end":63,"is_primary":false,"text":[{"text":"    for (name, &module, func_name) in functions_to_test.iter() {","highlight_start":39,"highlight_end":63}],"label":"this is an iterator with items of type `&(&str, *mut u8, &[u8])`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\main.rs","byte_start":43989,"byte_end":44013,"line_start":947,"line_end":947,"column_start":39,"column_end":63,"is_primary":false,"text":[{"text":"    for (name, &module, func_name) in functions_to_test.iter() {","highlight_start":39,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `for` loop","def_site_span":{"file_name":"src\\main.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"expected raw pointer `*mut u8`\n     found reference `&_`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider removing `&` from the pattern","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":43966,"byte_end":43967,"line_start":947,"line_end":947,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"    for (name, &module, func_name) in functions_to_test.iter() {","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:947:16\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m947\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (name, &module, func_name) in functions_to_test.iter() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis is an iterator with items of type `&(&str, *mut u8, &[u8])`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `*mut u8`, found `&_`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected raw pointer `\u001b[0m\u001b[0m\u001b[1m\u001b[35m*mut u8\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m                 found reference `\u001b[0m\u001b[0m\u001b[1m\u001b[35m&_\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider removing `&` from the pattern\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m947\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    for (name, \u001b[0m\u001b[0m\u001b[38;5;9m&\u001b[0m\u001b[0mmodule, func_name) in functions_to_test.iter() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m947\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    for (name, module, func_name) in functions_to_test.iter() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `ctx` as mutable, as it is not declared as mutable","code":{"code":"E0596","explanation":"This error occurs because you tried to mutably borrow a non-mutable variable.\n\nErroneous code example:\n\n```compile_fail,E0596\nlet x = 1;\nlet y = &mut x; // error: cannot borrow mutably\n```\n\nIn here, `x` isn't mutable, so when we try to mutably borrow it in `y`, it\nfails. To fix this error, you need to make `x` mutable:\n\n```\nlet mut x = 1;\nlet y = &mut x; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":12839,"byte_end":12842,"line_start":330,"line_end":330,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    ctx.logger.debug(&format!(\"Checking ActiveProcessorCount: {}\", ksd.active_processor_count));","highlight_start":5,"highlight_end":8}],"label":"cannot borrow as mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":12553,"byte_end":12556,"line_start":324,"line_end":324,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"    let ctx = app().lock().unwrap();","highlight_start":9,"highlight_end":12}],"label":"not mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":13087,"byte_end":13090,"line_start":334,"line_end":334,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    ctx.logger.debug(&format!(\"Checking KdDebuggerEnabled: {}\", ksd.kd_debugger_enabled));","highlight_start":5,"highlight_end":8}],"label":"cannot borrow as mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":13372,"byte_end":13375,"line_start":340,"line_end":340,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    ctx.logger.info(\"Checking for VDLL / Defender emulator...\");","highlight_start":5,"highlight_end":8}],"label":"cannot borrow as mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":13825,"byte_end":13828,"line_start":348,"line_end":348,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    ctx.logger.info(\"Checking for debugger with NtQueryInformationProcess...\");","highlight_start":5,"highlight_end":8}],"label":"cannot borrow as mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":15032,"byte_end":15035,"line_start":374,"line_end":374,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    ctx.logger.info(\"Security checks passed.\");","highlight_start":5,"highlight_end":8}],"label":"cannot borrow as mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider changing this to be mutable","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":12553,"byte_end":12553,"line_start":324,"line_end":324,"column_start":9,"column_end":9,"is_primary":true,"text":[{"text":"    let ctx = app().lock().unwrap();","highlight_start":9,"highlight_end":9}],"label":null,"suggested_replacement":"mut ","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0596]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot borrow `ctx` as mutable, as it is not declared as mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:324:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m324\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let ctx = app().lock().unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot mutable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m330\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ctx.logger.debug(&format!(\"Checking ActiveProcessorCount: {}\", ksd.active_processor_count));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mcannot borrow as mutable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ctx.logger.debug(&format!(\"Checking KdDebuggerEnabled: {}\", ksd.kd_debugger_enabled));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mcannot borrow as mutable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m340\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ctx.logger.info(\"Checking for VDLL / Defender emulator...\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mcannot borrow as mutable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ctx.logger.info(\"Checking for debugger with NtQueryInformationProcess...\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mcannot borrow as mutable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ctx.logger.info(\"Security checks passed.\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mcannot borrow as mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider changing this to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m324\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    let \u001b[0m\u001b[0m\u001b[38;5;10mmut \u001b[0m\u001b[0mctx = app().lock().unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `ctx` as mutable, as it is not declared as mutable","code":{"code":"E0596","explanation":"This error occurs because you tried to mutably borrow a non-mutable variable.\n\nErroneous code example:\n\n```compile_fail,E0596\nlet x = 1;\nlet y = &mut x; // error: cannot borrow mutably\n```\n\nIn here, `x` isn't mutable, so when we try to mutably borrow it in `y`, it\nfails. To fix this error, you need to make `x` mutable:\n\n```\nlet mut x = 1;\nlet y = &mut x; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":22245,"byte_end":22248,"line_start":539,"line_end":539,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        ctx.logger.debug(&format!(","highlight_start":9,"highlight_end":12}],"label":"cannot borrow as mutable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider changing this to be mutable","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":21174,"byte_end":21174,"line_start":515,"line_end":515,"column_start":9,"column_end":9,"is_primary":true,"text":[{"text":"    let ctx = app().lock().unwrap();","highlight_start":9,"highlight_end":9}],"label":null,"suggested_replacement":"mut ","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0596]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot borrow `ctx` as mutable, as it is not declared as mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:539:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m539\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ctx.logger.debug(&format!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot borrow as mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider changing this to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m515\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    let \u001b[0m\u001b[0m\u001b[38;5;10mmut \u001b[0m\u001b[0mctx = app().lock().unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 7 previous errors; 3 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 7 previous errors; 3 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0308, E0412, E0596, E0599, E0658.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0308, E0412, E0596, E0599, E0658.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0308`.\u001b[0m\n"}
